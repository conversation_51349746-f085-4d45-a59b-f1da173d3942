/*
 * ESP32 BLE Mill Monitor Master - Main Application Entry Point
 *
 * This file contains the main application initialization and coordination
 * of all system components including BLE, WiFi, file system, and HTTP server.
 */

#include <stdio.h>
#include <string.h>
#include "esp_log.h"
#include "esp_err.h"
#include "esp_system.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// Component headers
#include "ble_master.h"
#include "ble_uart.h"
#include "wifi_ap.h"
#include "fs_mount.h"
#include "file_server.h"

static const char *TAG = "MAIN";

/**
 * @brief UART接收数据回调函数示例
 *
 * 当UART接收到数据时会调用此函数
 *
 * @param data 接收到的数据指针
 * @param length 数据长度
 */
static void uart_rx_callback(const uint8_t *data, size_t length)
{
    ESP_LOGI(TAG, "UART received %zu bytes:", length);
    ESP_LOG_BUFFER_HEX(TAG, data, length);

    // 这里可以添加处理接收数据的逻辑
    // 例如：解析命令、转发到BLE等
}

// File system mount point
#define MOUNT_POINT "/spiffs"

void app_main(void)
{
    esp_err_t ret;

    // NVS（Non-Volatile Storage） 是一种非易失性存储系统，主要用于在设备掉电或重启后保留数据
    // 通常是以 key-value（键值对） 的形式存储数据, 保存配置参数（如 WiFi 名称、密码）
    ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // 初始化WiFi AP
    ret = wifi_init_ap();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "初始化WiFi失败: %s", esp_err_to_name(ret));
        return;
    }

    // 挂载文件系统
    const char *base_path = "/data";
    ret = example_mount_storage(base_path);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "挂载失败: %s", esp_err_to_name(ret));
        return;
    }

    // 启动文件服务器
    ret = example_start_file_server(base_path);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "初始化文件服务器失败: %s", esp_err_to_name(ret));
        return;
    }

    // 初始化UART用于BLE数据转发
    ret = ble_uart_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "初始化UART失败: %s", esp_err_to_name(ret));
        return;
    }
    else
    {
        // 设置UART接收回调函数
        ret = ble_uart_set_rx_callback(uart_rx_callback);
        if (ret != ESP_OK)
        {
            ESP_LOGE(TAG, "设置UART接收回调失败: %s", esp_err_to_name(ret));
        }
        else
        {
            ESP_LOGI(TAG, "UART接收回调设置成功");
        }
        // 初始化BLE Master功能
        ret = ble_master_init();
        if (ret != ESP_OK)
        {
            ESP_LOGE(TAG, "初始化 BLE Master: %s, 系统在5秒后重启!", esp_err_to_name(ret));
            vTaskDelay(pdMS_TO_TICKS(5000));
            esp_restart();
            return;
        }
        // 等待系统稳定后启动BLE协议栈
        vTaskDelay(pdMS_TO_TICKS(1000));

        // 启动BLE协议栈
        ret = ble_master_start();
        if (ret != ESP_OK)
        {
            ESP_LOGE(TAG, "启动BLE协议栈失败:  %s", esp_err_to_name(ret));
            return;
        }
    }
    // 主循环 - 系统监控
    while (1)
    {
        vTaskDelay(pdMS_TO_TICKS(10000)); // 每10秒检查一次
    }
}
