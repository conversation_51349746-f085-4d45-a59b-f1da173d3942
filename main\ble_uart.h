/*
 * SPDX-FileCopyrightText: 2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#ifndef BLE_UART_H
#define BLE_UART_H

#include <stdint.h>
#include "esp_err.h"

#ifdef __cplusplus
extern "C"
{
#endif

    /**
     * @brief UART接收数据回调函数类型定义
     *
     * 当UART接收到数据时会调用此回调函数
     *
     * @param data 接收到的数据指针
     * @param length 数据长度（字节数）
     */
    typedef void (*uart_rx_callback_t)(const uint8_t *data, size_t length);

    /**
     * @brief 初始化UART用于BLE数据转发
     *
     * 配置UART1用于将BLE从机数据转发到外部设备
     * - 波特率: 115200
     * - 数据位: 8
     * - 停止位: 1
     * - 校验位: 无
     * - TX引脚: GPIO15
     * - RX引脚: GPIO23
     *
     * @return
     *     - ESP_OK: 初始化成功
     *     - ESP_FAIL: 初始化失败
     */
    esp_err_t ble_uart_init(void);

    /**
     * @brief 通过UART发送原始二进制数据
     *
     * 将数据以原始二进制格式通过UART发送，不进行任何格式化处理
     *
     * @param data 要发送的数据指针
     * @param length 数据长度（字节数）
     *
     * @return
     *     - ESP_OK: 发送成功
     *     - ESP_FAIL: 发送失败
     *     - ESP_ERR_INVALID_ARG: 参数无效
     */
    esp_err_t ble_uart_send_raw_data(const uint8_t *data, size_t length);

    /**
     * @brief 设置UART接收数据回调函数
     *
     * 设置当UART接收到数据时的回调函数
     *
     * @param callback 回调函数指针，设置为NULL可取消回调
     *
     * @return
     *     - ESP_OK: 设置成功
     *     - ESP_FAIL: 设置失败
     */
    esp_err_t ble_uart_set_rx_callback(uart_rx_callback_t callback);

    /**
     * @brief 反初始化UART
     *
     * 释放UART资源，停止UART功能
     *
     * @return
     *     - ESP_OK: 反初始化成功
     *     - ESP_FAIL: 反初始化失败
     */
    esp_err_t ble_uart_deinit(void);

#ifdef __cplusplus
}
#endif

#endif /* BLE_UART_H */
